name: Update Algolia Search Index

on:
  push:
    branches: [main, master]
    paths: 
      - 'docs/**'
      - 'src/**'
  pull_request:
    branches: [main, master]
    paths:
      - 'docs/**'
      - 'src/**'
  workflow_dispatch: # Allow manual triggering

jobs:
  update-search-index:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm install
        
      - name: Update Algolia search index
        run: npm run index-algolia
        env:
          ALGOLIA_ADMIN_API_KEY: ${{ secrets.ALGOLIA_ADMIN_API_KEY }}
          
      - name: Notify success
        if: success()
        run: echo "✅ Algolia search index updated successfully!"
        
      - name: Notify failure
        if: failure()
        run: echo "❌ Failed to update Algolia search index"
